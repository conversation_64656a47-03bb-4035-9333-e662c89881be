#include "serializer.h"
#include <nlohmann/json.hpp>
#include <stdexcept>
#include "include/knowledge.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的序列化错误处理
template <typename T>
std::string SerializeWithErrorHandling(const T& request,
                                       const std::string& request_type) {
  try {
    nlohmann::json json_request;
    ToJson(json_request, request);
    return json_request.dump();
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON serialization failed for " + request_type +
                             ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Serialization failed for " + request_type + ": " +
                             e.what());
  }
}
}  // anonymous namespace

std::string SerializeChatRequest(const ChatRequest& request) {
  return SerializeWithErrorHandling(request, "ChatRequest");
}

std::string SerializeTaskStopRequest(const TaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "TaskStopRequest");
}

std::string SerializeFeedbackRequest(const FeedbackRequest& request) {
  return SerializeWithErrorHandling(request, "FeedbackRequest");
}

std::string SerializeDeleteConversationRequest(
    const DeleteConversationRequest& request) {
  return SerializeWithErrorHandling(request, "DeleteConversationRequest");
}

std::string SerializeRenameConversationRequest(
    const RenameConversationRequest& request) {
  return SerializeWithErrorHandling(request, "RenameConversationRequest");
}

std::string SerializeSuggestedRequest(const SuggestedRequest& request) {
  return SerializeWithErrorHandling(request, "SuggestedRequest");
}

std::string SerializeMessagesRequest(const MessagesRequest& request) {
  return SerializeWithErrorHandling(request, "MessagesRequest");
}

std::string SerializeConversationRequest(const ConversationRequest& request) {
  return SerializeWithErrorHandling(request, "ConversationRequest");
}
std::string SerializeChatCompletionRequest(
    const CompletionMessageRequest& request) {
  return SerializeWithErrorHandling(request, "ChatCompletionRequest");
}
std::string SerializeWorkflowRunRequest(const WorkflowRunRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowRunRequest");
}
std::string SerializeWorkflowTaskStopRequest(
    const WorkflowTaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowTaskStopRequest");
}
std::string SerializeWorkflowLogsRequest(const WorkflowLogsRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowLogsRequest");
}

// Knowledge base serialization implementation
std::string SerializeCreateDocumentByTextRequest(
    const CreateDocumentByTextRequest& request) {
  nlohmann::json json_request;

  json_request["name"] = request.GetName();
  json_request["text"] = request.GetText();
  json_request["indexing_technique"] = request.GetIndexingTechnique();

  const auto& process_rule = request.GetProcessRule();
  nlohmann::json process_rule_json;
  process_rule_json["mode"] = process_rule.mode;

  if (process_rule.mode == "custom") {
    process_rule_json["rules"] = process_rule.rules;

    nlohmann::json pre_processing_rules = nlohmann::json::array();
    for (const auto& rule : process_rule.pre_processing_rules) {
      nlohmann::json rule_json;
      rule_json["id"] = rule.id;
      rule_json["enabled"] = rule.enabled;
      pre_processing_rules.push_back(rule_json);
    }
    process_rule_json["pre_processing_rules"] = pre_processing_rules;
    nlohmann::json segmentation_json;
    segmentation_json["separator"] = process_rule.segmentation.separator;
    segmentation_json["max_tokens"] = process_rule.segmentation.max_tokens;
    process_rule_json["segmentation"] = segmentation_json;
  }
  json_request["process_rule"] = process_rule_json;
  return json_request.dump();
}

}  // namespace amssdk
