#include "serializer.h"
#include <nlohmann/json.hpp>
#include <stdexcept>
#include "include/knowledge.h"
#include "include/knowledge_extended.h"
#include "include/knowledge_segments.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的序列化错误处理
template <typename T>
std::string SerializeWithErrorHandling(const T& request,
                                       const std::string& request_type) {
  try {
    nlohmann::json json_request;
    ToJson(json_request, request);
    return json_request.dump();
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON serialization failed for " + request_type +
                             ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Serialization failed for " + request_type + ": " +
                             e.what());
  }
}
}  // anonymous namespace

std::string SerializeChatRequest(const ChatRequest& request) {
  return SerializeWithErrorHandling(request, "ChatRequest");
}

std::string SerializeTaskStopRequest(const TaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "TaskStopRequest");
}

std::string SerializeFeedbackRequest(const FeedbackRequest& request) {
  return SerializeWithErrorHandling(request, "FeedbackRequest");
}

std::string SerializeDeleteConversationRequest(
    const DeleteConversationRequest& request) {
  return SerializeWithErrorHandling(request, "DeleteConversationRequest");
}

std::string SerializeRenameConversationRequest(
    const RenameConversationRequest& request) {
  return SerializeWithErrorHandling(request, "RenameConversationRequest");
}

std::string SerializeSuggestedRequest(const SuggestedRequest& request) {
  return SerializeWithErrorHandling(request, "SuggestedRequest");
}

std::string SerializeMessagesRequest(const MessagesRequest& request) {
  return SerializeWithErrorHandling(request, "MessagesRequest");
}

std::string SerializeConversationRequest(const ConversationRequest& request) {
  return SerializeWithErrorHandling(request, "ConversationRequest");
}
std::string SerializeChatCompletionRequest(
    const CompletionMessageRequest& request) {
  return SerializeWithErrorHandling(request, "ChatCompletionRequest");
}
std::string SerializeWorkflowRunRequest(const WorkflowRunRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowRunRequest");
}
std::string SerializeWorkflowTaskStopRequest(
    const WorkflowTaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowTaskStopRequest");
}
std::string SerializeWorkflowLogsRequest(const WorkflowLogsRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowLogsRequest");
}

// Knowledge base serialization implementation
std::string SerializeCreateDocumentByTextRequest(
    const CreateDocumentByTextRequest& request) {
  nlohmann::json json_request;

  json_request["name"] = request.GetName();
  json_request["text"] = request.GetText();
  json_request["indexing_technique"] = request.GetIndexingTechnique();

  const auto& process_rule = request.GetProcessRule();
  nlohmann::json process_rule_json;
  process_rule_json["mode"] = process_rule.mode;

  if (process_rule.mode == "custom") {
    process_rule_json["rules"] = process_rule.rules;

    nlohmann::json pre_processing_rules = nlohmann::json::array();
    for (const auto& rule : process_rule.pre_processing_rules) {
      nlohmann::json rule_json;
      rule_json["id"] = rule.id;
      rule_json["enabled"] = rule.enabled;
      pre_processing_rules.push_back(rule_json);
    }
    process_rule_json["pre_processing_rules"] = pre_processing_rules;
    nlohmann::json segmentation_json;
    segmentation_json["separator"] = process_rule.segmentation.separator;
    segmentation_json["max_tokens"] = process_rule.segmentation.max_tokens;
    process_rule_json["segmentation"] = segmentation_json;
  }
  json_request["process_rule"] = process_rule_json;
  return json_request;
}

// 知识库管理序列化实现
std::string SerializeCreateDatasetRequest(const CreateDatasetRequest& request) {
  nlohmann::json json_request;
  json_request["name"] = request.GetName();
  if (!request.GetDescription().empty()) {
    json_request["description"] = request.GetDescription();
  }
  if (!request.GetIndexingTechnique().empty()) {
    json_request["indexing_technique"] = request.GetIndexingTechnique();
  }
  json_request["permission"] = request.GetPermission();
  json_request["provider"] = request.GetProvider();
  return json_request;
}

std::string SerializeListDatasetsRequest(const ListDatasetsRequest& request) {
  std::string query_params = "page=" + std::to_string(request.GetPage()) +
                             "&limit=" + std::to_string(request.GetLimit());
  return query_params;
}

std::string SerializeDeleteDatasetRequest(const DeleteDatasetRequest& request) {
  // DELETE请求通常不需要body，dataset_id在URL中
  return "";
}

// 文档管理序列化实现
std::string SerializeCreateDocumentByFileRequest(
    const CreateDocumentByFileRequest& request) {
  nlohmann::json json_request;
  if (!request.GetIndexingTechnique().empty()) {
    json_request["indexing_technique"] = request.GetIndexingTechnique();
  }
  if (!request.GetOriginalDocumentId().empty()) {
    json_request["original_document_id"] = request.GetOriginalDocumentId();
  }

  // 序列化处理规则
  const auto& process_rule = request.GetProcessRule();
  nlohmann::json process_rule_json;
  process_rule_json["mode"] = process_rule.mode;
  if (process_rule.mode == "custom") {
    process_rule_json["rules"] = process_rule.rules;
  }
  json_request["process_rule"] = process_rule_json;

  return json_request;
}

std::string SerializeUpdateDocumentByTextRequest(
    const UpdateDocumentByTextRequest& request) {
  nlohmann::json json_request;
  if (!request.GetName().empty()) {
    json_request["name"] = request.GetName();
  }
  if (!request.GetText().empty()) {
    json_request["text"] = request.GetText();
  }

  // 序列化处理规则
  const auto& process_rule = request.GetProcessRule();
  nlohmann::json process_rule_json;
  process_rule_json["mode"] = process_rule.mode;
  if (process_rule.mode == "custom") {
    process_rule_json["rules"] = process_rule.rules;
  }
  json_request["process_rule"] = process_rule_json;

  return json_request;
}

std::string SerializeUpdateDocumentByFileRequest(
    const UpdateDocumentByFileRequest& request) {
  nlohmann::json json_request;
  if (!request.GetName().empty()) {
    json_request["name"] = request.GetName();
  }

  // 序列化处理规则
  const auto& process_rule = request.GetProcessRule();
  nlohmann::json process_rule_json;
  process_rule_json["mode"] = process_rule.mode;
  if (process_rule.mode == "custom") {
    process_rule_json["rules"] = process_rule.rules;
  }
  json_request["process_rule"] = process_rule_json;

  return json_request;
}

std::string SerializeGetIndexingStatusRequest(
    const GetIndexingStatusRequest& request) {
  return "";
}

std::string SerializeDeleteDocumentRequest(
    const DeleteDocumentRequest& request) {
  return "";
}

std::string SerializeListDocumentsRequest(const ListDocumentsRequest& request) {
  std::string query_params = "page=" + std::to_string(request.GetPage()) +
                             "&limit=" + std::to_string(request.GetLimit());
  if (!request.GetKeyword().empty()) {
    query_params += "&keyword=" + request.GetKeyword();
  }
  return query_params;
}

// 分段管理序列化实现
std::string SerializeCreateSegmentRequest(const CreateSegmentRequest& request) {
  nlohmann::json json_request;
  nlohmann::json segments_array = nlohmann::json::array();

  for (const auto& segment : request.GetSegments()) {
    nlohmann::json segment_json;
    segment_json["content"] = segment.content;
    if (!segment.answer.empty()) {
      segment_json["answer"] = segment.answer;
    }
    if (!segment.keywords.empty()) {
      segment_json["keywords"] = segment.keywords;
    }
    segments_array.push_back(segment_json);
  }

  json_request["segments"] = segments_array;
  return json_request;
}

std::string SerializeListSegmentsRequest(const ListSegmentsRequest& request) {
  std::string query_params = "";
  if (!request.GetKeyword().empty()) {
    query_params += "keyword=" + request.GetKeyword();
  }
  if (!request.GetStatus().empty()) {
    if (!query_params.empty())
      query_params += "&";
    query_params += "status=" + request.GetStatus();
  }
  return query_params;
}

std::string SerializeDeleteSegmentRequest(const DeleteSegmentRequest& request) {
  return "";
}

std::string SerializeUpdateSegmentRequest(const UpdateSegmentRequest& request) {
  nlohmann::json json_request;
  nlohmann::json segment_json;

  const auto& segment = request.GetSegment();
  segment_json["content"] = segment.content;
  if (!segment.answer.empty()) {
    segment_json["answer"] = segment.answer;
  }
  if (!segment.keywords.empty()) {
    segment_json["keywords"] = segment.keywords;
  }
  segment_json["enabled"] = segment.enabled;

  json_request["segment"] = segment_json;
  return json_request;
}

// 检索序列化实现
std::string SerializeRetrieveDatasetRequest(
    const RetrieveDatasetRequest& request) {
  nlohmann::json json_request;
  json_request["query"] = request.GetQuery();

  const auto& retrieval_model = request.GetRetrievalModel();
  nlohmann::json retrieval_model_json;
  retrieval_model_json["search_method"] = retrieval_model.search_method;
  retrieval_model_json["reranking_enable"] = retrieval_model.reranking_enable;
  retrieval_model_json["reranking_mode"] = retrieval_model.reranking_mode;
  retrieval_model_json["weights"] = retrieval_model.weights;
  retrieval_model_json["top_k"] = retrieval_model.top_k;
  retrieval_model_json["score_threshold_enabled"] =
      retrieval_model.score_threshold_enabled;
  retrieval_model_json["score_threshold"] = retrieval_model.score_threshold;

  json_request["retrieval_model"] = retrieval_model_json;
  return json_request;
}

}  // namespace amssdk
