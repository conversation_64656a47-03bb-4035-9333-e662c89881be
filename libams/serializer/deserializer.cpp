#include "deserializer.h"
#include <memory>
#include <nlohmann/json.hpp>
#include <stdexcept>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "include/knowledge.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的反序列化错误处理
template <typename T>
T DeserializeWithErrorHandling(const nlohmann::json& j,
                               const std::string& type_name) {
  try {
    T result;
    FromJson(j, result);  // 调用底层from_json函数
    return result;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for " + type_name +
                             ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for " + type_name + ": " +
                             e.what());
  }
}
}  // namespace

FileResponse DeserializeFileResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<FileResponse>(j, "FileResponse");
}

ConversationResponse DeserializeConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<ConversationResponse>(
      j, "ConversationResponse");
}

SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SuggestedResponse>(j,
                                                         "SuggestedResponse");
}

MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<MessagesResponse>(j, "MessagesResponse");
}

SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SimpleResponse>(j, "SimpleResponse");
}

RenameConversationResponse DeserializeRenameConversationResponse(
    const nlohmann::json& j) {
  return DeserializeWithErrorHandling<RenameConversationResponse>(
      j, "RenameConversationResponse");
}

WorkflowRunResponse DeserializeWorkflowRunResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowRunResponse>(
      j, "WorkflowRunResponse");
}
WorkflowRunInfoResponse DeserializeWorkflowRunInfoResponse(
    const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowRunInfoResponse>(
      j, "WorkflowRunInfoResponse");
}
WorkflowLogsResponse DeserializeWorkflowLogsResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowLogsResponse>(
      j, "WorkflowLogsResponse");
}

// Knowledge base deserialization implementation
CreateDocumentByTextResponse DeserializeCreateDocumentByTextResponse(
    const nlohmann::json& j) {
  CreateDocumentByTextResponse response;

  if (j.contains("document")) {
    const auto& doc_json = j["document"];
    DocumentInfo document;

    if (doc_json.contains("id")) document.id = doc_json["id"];
    if (doc_json.contains("position")) document.position = doc_json["position"];
    if (doc_json.contains("data_source_type"))
      document.data_source_type = doc_json["data_source_type"];
    if (doc_json.contains("data_source_info") &&
        doc_json["data_source_info"].contains("upload_file_id")) {
      document.data_source_info.upload_file_id =
          doc_json["data_source_info"]["upload_file_id"];
    }
    if (doc_json.contains("dataset_process_rule_id"))
      document.dataset_process_rule_id = doc_json["dataset_process_rule_id"];
    if (doc_json.contains("name")) document.name = doc_json["name"];
    if (doc_json.contains("created_from"))
      document.created_from = doc_json["created_from"];
    if (doc_json.contains("created_by"))
      document.created_by = doc_json["created_by"];
    if (doc_json.contains("created_at"))
      document.created_at = doc_json["created_at"];
    if (doc_json.contains("tokens")) document.tokens = doc_json["tokens"];
    if (doc_json.contains("indexing_status"))
      document.indexing_status = doc_json["indexing_status"];
    if (doc_json.contains("error") && !doc_json["error"].is_null())
      document.error = doc_json["error"];
    if (doc_json.contains("enabled")) document.enabled = doc_json["enabled"];
    if (doc_json.contains("disabled_at"))
      document.disabled_at = doc_json["disabled_at"];
    if (doc_json.contains("disabled_by") && !doc_json["disabled_by"].is_null())
      document.disabled_by = doc_json["disabled_by"];
    if (doc_json.contains("archived")) document.archived = doc_json["archived"];
    if (doc_json.contains("display_status"))
      document.display_status = doc_json["display_status"];
    if (doc_json.contains("word_count"))
      document.word_count = doc_json["word_count"];
    if (doc_json.contains("hit_count"))
      document.hit_count = doc_json["hit_count"];
    if (doc_json.contains("doc_form"))
      document.doc_form = doc_json["doc_form"];

    response.SetDocument(document);
  }

  if (j.contains("batch")) {
    response.SetBatch(j["batch"]);
  }

  return response;
}

}  // namespace amssdk