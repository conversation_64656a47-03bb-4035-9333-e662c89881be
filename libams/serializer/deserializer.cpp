#include "deserializer.h"
#include <memory>
#include <nlohmann/json.hpp>
#include <stdexcept>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的反序列化错误处理
template <typename T>
T DeserializeWithErrorHandling(const nlohmann::json& j,
                               const std::string& type_name) {
  try {
    T result;
    FromJson(j, result);  // 调用底层from_json函数
    return result;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for " + type_name +
                             ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for " + type_name + ": " +
                             e.what());
  }
}
}  // namespace

FileResponse DeserializeFileResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<FileResponse>(j, "FileResponse");
}

ConversationResponse DeserializeConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<ConversationResponse>(
      j, "ConversationResponse");
}

SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SuggestedResponse>(j,
                                                         "SuggestedResponse");
}

MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<MessagesResponse>(j, "MessagesResponse");
}

SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SimpleResponse>(j, "SimpleResponse");
}

RenameConversationResponse DeserializeRenameConversationResponse(
    const nlohmann::json& j) {
  return DeserializeWithErrorHandling<RenameConversationResponse>(
      j, "RenameConversationResponse");
}

WorkflowRunResponse DeserializeWorkflowRunResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowRunResponse>(
      j, "WorkflowRunResponse");
}
WorkflowRunInfoResponse DeserializeWorkflowRunInfoResponse(
    const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowRunInfoResponse>(
      j, "WorkflowRunInfoResponse");
}
WorkflowLogsResponse DeserializeWorkflowLogsResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowLogsResponse>(
      j, "WorkflowLogsResponse");
}

}  // namespace amssdk