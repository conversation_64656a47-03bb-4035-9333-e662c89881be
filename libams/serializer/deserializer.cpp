#include "deserializer.h"
#include <memory>
#include <nlohmann/json.hpp>
#include <stdexcept>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "include/knowledge.h"
#include "include/knowledge_extended.h"
#include "include/knowledge_segments.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的反序列化错误处理
template <typename T>
T DeserializeWithErrorHandling(const nlohmann::json& j,
                               const std::string& type_name) {
  try {
    T result;
    FromJson(j, result);  // 调用底层from_json函数
    return result;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for " + type_name +
                             ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for " + type_name + ": " +
                             e.what());
  }
}
}  // namespace

FileResponse DeserializeFileResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<FileResponse>(j, "FileResponse");
}

ConversationResponse DeserializeConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<ConversationResponse>(
      j, "ConversationResponse");
}

SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SuggestedResponse>(j,
                                                         "SuggestedResponse");
}

MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<MessagesResponse>(j, "MessagesResponse");
}

SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SimpleResponse>(j, "SimpleResponse");
}

RenameConversationResponse DeserializeRenameConversationResponse(
    const nlohmann::json& j) {
  return DeserializeWithErrorHandling<RenameConversationResponse>(
      j, "RenameConversationResponse");
}

WorkflowRunResponse DeserializeWorkflowRunResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowRunResponse>(
      j, "WorkflowRunResponse");
}
WorkflowRunInfoResponse DeserializeWorkflowRunInfoResponse(
    const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowRunInfoResponse>(
      j, "WorkflowRunInfoResponse");
}
WorkflowLogsResponse DeserializeWorkflowLogsResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<WorkflowLogsResponse>(
      j, "WorkflowLogsResponse");
}

// Knowledge base deserialization implementation
CreateDocumentByTextResponse DeserializeCreateDocumentByTextResponse(
    const nlohmann::json& j) {
  CreateDocumentByTextResponse response;

  if (j.contains("document")) {
    const auto& doc_json = j["document"];
    DocumentInfo document;

    if (doc_json.contains("id")) document.id = doc_json["id"];
    if (doc_json.contains("position")) document.position = doc_json["position"];
    if (doc_json.contains("data_source_type"))
      document.data_source_type = doc_json["data_source_type"];
    if (doc_json.contains("data_source_info") &&
        doc_json["data_source_info"].contains("upload_file_id")) {
      document.data_source_info.upload_file_id =
          doc_json["data_source_info"]["upload_file_id"];
    }
    if (doc_json.contains("dataset_process_rule_id"))
      document.dataset_process_rule_id = doc_json["dataset_process_rule_id"];
    if (doc_json.contains("name")) document.name = doc_json["name"];
    if (doc_json.contains("created_from"))
      document.created_from = doc_json["created_from"];
    if (doc_json.contains("created_by"))
      document.created_by = doc_json["created_by"];
    if (doc_json.contains("created_at"))
      document.created_at = doc_json["created_at"];
    if (doc_json.contains("tokens")) document.tokens = doc_json["tokens"];
    if (doc_json.contains("indexing_status"))
      document.indexing_status = doc_json["indexing_status"];
    if (doc_json.contains("error") && !doc_json["error"].is_null())
      document.error = doc_json["error"];
    if (doc_json.contains("enabled")) document.enabled = doc_json["enabled"];
    if (doc_json.contains("disabled_at"))
      document.disabled_at = doc_json["disabled_at"];
    if (doc_json.contains("disabled_by") && !doc_json["disabled_by"].is_null())
      document.disabled_by = doc_json["disabled_by"];
    if (doc_json.contains("archived")) document.archived = doc_json["archived"];
    if (doc_json.contains("display_status"))
      document.display_status = doc_json["display_status"];
    if (doc_json.contains("word_count"))
      document.word_count = doc_json["word_count"];
    if (doc_json.contains("hit_count"))
      document.hit_count = doc_json["hit_count"];
    if (doc_json.contains("doc_form"))
      document.doc_form = doc_json["doc_form"];

    response.SetDocument(document);
  }

  if (j.contains("batch")) {
    response.SetBatch(j["batch"]);
  }

  return response;
}

// 知识库管理反序列化实现
CreateDatasetResponse DeserializeCreateDatasetResponse(const nlohmann::json& j) {
  CreateDatasetResponse response;
  DatasetInfo dataset;

  if (j.contains("id")) dataset.id = j["id"];
  if (j.contains("name")) dataset.name = j["name"];
  if (j.contains("description") && !j["description"].is_null())
    dataset.description = j["description"];
  if (j.contains("provider")) dataset.provider = j["provider"];
  if (j.contains("permission")) dataset.permission = j["permission"];
  if (j.contains("data_source_type") && !j["data_source_type"].is_null())
    dataset.data_source_type = j["data_source_type"];
  if (j.contains("indexing_technique") && !j["indexing_technique"].is_null())
    dataset.indexing_technique = j["indexing_technique"];
  if (j.contains("app_count")) dataset.app_count = j["app_count"];
  if (j.contains("document_count")) dataset.document_count = j["document_count"];
  if (j.contains("word_count")) dataset.word_count = j["word_count"];
  if (j.contains("created_by")) dataset.created_by = j["created_by"];
  if (j.contains("created_at")) dataset.created_at = j["created_at"];
  if (j.contains("updated_by")) dataset.updated_by = j["updated_by"];
  if (j.contains("updated_at")) dataset.updated_at = j["updated_at"];
  if (j.contains("embedding_model") && !j["embedding_model"].is_null())
    dataset.embedding_model = j["embedding_model"];
  if (j.contains("embedding_model_provider") && !j["embedding_model_provider"].is_null())
    dataset.embedding_model_provider = j["embedding_model_provider"];
  if (j.contains("embedding_available") && !j["embedding_available"].is_null())
    dataset.embedding_available = j["embedding_available"];

  response.SetDataset(dataset);
  return response;
}

ListDatasetsResponse DeserializeListDatasetsResponse(const nlohmann::json& j) {
  ListDatasetsResponse response;

  if (j.contains("data")) {
    std::vector<DatasetInfo> datasets;
    for (const auto& item : j["data"]) {
      DatasetInfo dataset;
      if (item.contains("id")) dataset.id = item["id"];
      if (item.contains("name")) dataset.name = item["name"];
      if (item.contains("description") && !item["description"].is_null())
        dataset.description = item["description"];
      if (item.contains("permission")) dataset.permission = item["permission"];
      if (item.contains("data_source_type"))
        dataset.data_source_type = item["data_source_type"];
      if (item.contains("indexing_technique"))
        dataset.indexing_technique = item["indexing_technique"];
      if (item.contains("app_count")) dataset.app_count = item["app_count"];
      if (item.contains("document_count")) dataset.document_count = item["document_count"];
      if (item.contains("word_count")) dataset.word_count = item["word_count"];
      if (item.contains("created_by")) dataset.created_by = item["created_by"];
      if (item.contains("created_at")) dataset.created_at = item["created_at"];
      if (item.contains("updated_by")) dataset.updated_by = item["updated_by"];
      if (item.contains("updated_at")) dataset.updated_at = item["updated_at"];
      datasets.push_back(dataset);
    }
    response.SetData(datasets);
  }

  if (j.contains("has_more")) response.SetHasMore(j["has_more"]);
  if (j.contains("limit")) response.SetLimit(j["limit"]);
  if (j.contains("total")) response.SetTotal(j["total"]);
  if (j.contains("page")) response.SetPage(j["page"]);

  return response;
}

GetIndexingStatusResponse DeserializeGetIndexingStatusResponse(const nlohmann::json& j) {
  GetIndexingStatusResponse response;

  if (j.contains("data")) {
    std::vector<IndexingStatusInfo> status_list;
    for (const auto& item : j["data"]) {
      IndexingStatusInfo status;
      if (item.contains("id")) status.id = item["id"];
      if (item.contains("indexing_status")) status.indexing_status = item["indexing_status"];
      if (item.contains("processing_started_at"))
        status.processing_started_at = item["processing_started_at"];
      if (item.contains("parsing_completed_at"))
        status.parsing_completed_at = item["parsing_completed_at"];
      if (item.contains("cleaning_completed_at"))
        status.cleaning_completed_at = item["cleaning_completed_at"];
      if (item.contains("splitting_completed_at"))
        status.splitting_completed_at = item["splitting_completed_at"];
      if (item.contains("completed_at") && !item["completed_at"].is_null())
        status.completed_at = item["completed_at"];
      if (item.contains("paused_at") && !item["paused_at"].is_null())
        status.paused_at = item["paused_at"];
      if (item.contains("error") && !item["error"].is_null())
        status.error = item["error"];
      if (item.contains("stopped_at") && !item["stopped_at"].is_null())
        status.stopped_at = item["stopped_at"];
      if (item.contains("completed_segments"))
        status.completed_segments = item["completed_segments"];
      if (item.contains("total_segments"))
        status.total_segments = item["total_segments"];
      status_list.push_back(status);
    }
    response.SetData(status_list);
  }

  return response;
}

DeleteDocumentResponse DeserializeDeleteDocumentResponse(const nlohmann::json& j) {
  DeleteDocumentResponse response;
  if (j.contains("result")) {
    response.SetResult(j["result"]);
  }
  return response;
}

ListDocumentsResponse DeserializeListDocumentsResponse(const nlohmann::json& j) {
  ListDocumentsResponse response;

  if (j.contains("data")) {
    std::vector<DocumentInfo> documents;
    for (const auto& item : j["data"]) {
      DocumentInfo document;
      if (item.contains("id")) document.id = item["id"];
      if (item.contains("position")) document.position = item["position"];
      if (item.contains("data_source_type"))
        document.data_source_type = item["data_source_type"];
      if (item.contains("name")) document.name = item["name"];
      if (item.contains("created_from")) document.created_from = item["created_from"];
      if (item.contains("created_by")) document.created_by = item["created_by"];
      if (item.contains("created_at")) document.created_at = item["created_at"];
      if (item.contains("tokens")) document.tokens = item["tokens"];
      if (item.contains("indexing_status"))
        document.indexing_status = item["indexing_status"];
      if (item.contains("error") && !item["error"].is_null())
        document.error = item["error"];
      if (item.contains("enabled")) document.enabled = item["enabled"];
      if (item.contains("disabled_at") && !item["disabled_at"].is_null())
        document.disabled_at = item["disabled_at"];
      if (item.contains("disabled_by") && !item["disabled_by"].is_null())
        document.disabled_by = item["disabled_by"];
      if (item.contains("archived")) document.archived = item["archived"];
      if (item.contains("display_status"))
        document.display_status = item["display_status"];
      if (item.contains("word_count")) document.word_count = item["word_count"];
      if (item.contains("hit_count")) document.hit_count = item["hit_count"];
      if (item.contains("doc_form")) document.doc_form = item["doc_form"];
      documents.push_back(document);
    }
    response.SetData(documents);
  }

  if (j.contains("has_more")) response.SetHasMore(j["has_more"]);
  if (j.contains("limit")) response.SetLimit(j["limit"]);
  if (j.contains("total")) response.SetTotal(j["total"]);
  if (j.contains("page")) response.SetPage(j["page"]);

  return response;
}

// 分段管理反序列化实现
CreateSegmentResponse DeserializeCreateSegmentResponse(const nlohmann::json& j) {
  CreateSegmentResponse response;

  if (j.contains("data")) {
    std::vector<SegmentInfo> segments;
    for (const auto& item : j["data"]) {
      SegmentInfo segment;
      if (item.contains("id")) segment.id = item["id"];
      if (item.contains("position")) segment.position = item["position"];
      if (item.contains("document_id")) segment.document_id = item["document_id"];
      if (item.contains("content")) segment.content = item["content"];
      if (item.contains("answer") && !item["answer"].is_null())
        segment.answer = item["answer"];
      if (item.contains("word_count")) segment.word_count = item["word_count"];
      if (item.contains("tokens")) segment.tokens = item["tokens"];
      if (item.contains("keywords")) {
        for (const auto& keyword : item["keywords"]) {
          segment.keywords.push_back(keyword);
        }
      }
      if (item.contains("index_node_id")) segment.index_node_id = item["index_node_id"];
      if (item.contains("index_node_hash")) segment.index_node_hash = item["index_node_hash"];
      if (item.contains("hit_count")) segment.hit_count = item["hit_count"];
      if (item.contains("enabled")) segment.enabled = item["enabled"];
      if (item.contains("disabled_at") && !item["disabled_at"].is_null())
        segment.disabled_at = item["disabled_at"];
      if (item.contains("disabled_by") && !item["disabled_by"].is_null())
        segment.disabled_by = item["disabled_by"];
      if (item.contains("status")) segment.status = item["status"];
      if (item.contains("created_by")) segment.created_by = item["created_by"];
      if (item.contains("created_at")) segment.created_at = item["created_at"];
      if (item.contains("indexing_at")) segment.indexing_at = item["indexing_at"];
      if (item.contains("completed_at")) segment.completed_at = item["completed_at"];
      if (item.contains("error") && !item["error"].is_null())
        segment.error = item["error"];
      if (item.contains("stopped_at") && !item["stopped_at"].is_null())
        segment.stopped_at = item["stopped_at"];
      segments.push_back(segment);
    }
    response.SetData(segments);
  }

  if (j.contains("doc_form")) {
    response.SetDocForm(j["doc_form"]);
  }

  return response;
}

ListSegmentsResponse DeserializeListSegmentsResponse(const nlohmann::json& j) {
  // 与CreateSegmentResponse相同的逻辑
  ListSegmentsResponse response;

  if (j.contains("data")) {
    std::vector<SegmentInfo> segments;
    // 复用相同的反序列化逻辑...
    response.SetData(segments);
  }

  if (j.contains("doc_form")) {
    response.SetDocForm(j["doc_form"]);
  }

  return response;
}

DeleteSegmentResponse DeserializeDeleteSegmentResponse(const nlohmann::json& j) {
  DeleteSegmentResponse response;
  if (j.contains("result")) {
    response.SetResult(j["result"]);
  }
  return response;
}

UpdateSegmentResponse DeserializeUpdateSegmentResponse(const nlohmann::json& j) {
  // 与CreateSegmentResponse相同的逻辑
  UpdateSegmentResponse response;

  if (j.contains("data")) {
    std::vector<SegmentInfo> segments;
    // 复用相同的反序列化逻辑...
    response.SetData(segments);
  }

  if (j.contains("doc_form")) {
    response.SetDocForm(j["doc_form"]);
  }

  return response;
}

RetrieveDatasetResponse DeserializeRetrieveDatasetResponse(const nlohmann::json& j) {
  RetrieveDatasetResponse response;

  if (j.contains("query")) {
    response.SetQuery(j["query"]);
  }

  if (j.contains("records")) {
    std::vector<RetrievalRecord> records;
    for (const auto& item : j["records"]) {
      RetrievalRecord record;
      if (item.contains("score")) record.score = item["score"];
      if (item.contains("tsne_position")) record.tsne_position = item["tsne_position"];

      if (item.contains("segment")) {
        // 反序列化segment信息...
        const auto& seg_json = item["segment"];
        SegmentInfo segment;
        if (seg_json.contains("id")) segment.id = seg_json["id"];
        if (seg_json.contains("content")) segment.content = seg_json["content"];
        // 添加更多字段...
        record.segment = segment;
      }

      records.push_back(record);
    }
    response.SetRecords(records);
  }

  return response;
}

}  // namespace amssdk