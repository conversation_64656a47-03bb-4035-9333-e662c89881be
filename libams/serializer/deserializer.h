#ifndef AMSSDK_DESERIALIZER_H
#define AMSSDK_DESERIALIZER_H

#include <nlohmann/json.hpp>

namespace amssdk {

class StreamEvent;
class FileResponse;
class SimpleResponse;
class ConversationResponse;
class SuggestedResponse;
class MessagesResponse;
class RenameConversationResponse;
class WorkflowRunResponse;
class WorkflowRunInfoResponse;
class WorkflowLogsResponse;

// Knowledge base classes
class CreateDocumentByTextResponse;

FileResponse DeserializeFileResponse(const nlohmann::json& j);
ConversationResponse DeserializeConversationResponse(const nlohmann::json& j);
SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j);
MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j);
SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j);
RenameConversationResponse DeserializeRenameConversationResponse(
    const nlohmann::json& j);
WorkflowRunResponse DeserializeWorkflowRunResponse(const nlohmann::json& j);
WorkflowRunInfoResponse DeserializeWorkflowRunInfoResponse(
    const nlohmann::json& j);
WorkflowLogsResponse DeserializeWorkflowLogsResponse(const nlohmann::json& j);

// Knowledge base deserialization functions
CreateDocumentByTextResponse DeserializeCreateDocumentByTextResponse(
    const nlohmann::json& j);

}  // namespace amssdk
#endif  //AMSSDK_DESERIALIZER_H
