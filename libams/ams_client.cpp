#include "ams_client.h"

#include "api/api_manager.h"
#include "api/services/app_service.h"
#include "api/services/audio_service.h"
#include "api/services/chat_service.h"
#include "api/services/conversation_service.h"
#include "api/services/file_service.h"
#include "api/services/task_service.h"
#include "api/services/workflow_service.h"
#include "api/services/knowledge_service.h"
#include "include/app.h"
#include "include/audio.h"
#include "include/workflow.h"

namespace amssdk {

AmsClient::AmsClient(const std::string& base_url)
    : api_manager_(std::make_unique<ApiManager>(base_url)) {}

AmsClient::~AmsClient() = default;

bool AmsClient::SetAuthorizationKey(const std::string& key) const {
  return api_manager_->SetAuthorizationKey(key);
}

void AmsClient::SetMaxTimeout(int32_t ms) const {
  api_manager_->SetMaxTimeout(ms);
}

ApiResult<void> AmsClient::SendChatMessage(
    const ChatRequest& request, const StreamEventCallback& callback) const {
  return api_manager_->chat().SendChatMessage(request, callback);
}
ApiResult<void> AmsClient::SendCompletionMessage(
    const CompletionMessageRequest& request,
    const StreamEventCallback& callback) const {
  return api_manager_->chat().SendCompletionMessage(request, callback);
}

ApiResult<FileResponse> AmsClient::FileUpload(
    const FileRequest& request) const {
  return api_manager_->file().Upload(request.GetFilePath(), request.GetUser());
}
ApiResult<SimpleResponse> AmsClient::StopTask(
    const TaskStopRequest& request) const {
  return api_manager_->task().StopTask(request);
}
ApiResult<SimpleResponse> AmsClient::SendFeedback(
    const FeedbackRequest& request) const {
  return api_manager_->task().SendFeedback(request);
}
ApiResult<SuggestedResponse> AmsClient::GetSuggested(
    const SuggestedRequest& request) const {
  return api_manager_->conversation().GetSuggested(request);
}
ApiResult<MessagesResponse> AmsClient::GetMessages(
    const MessagesRequest& request) const {
  return api_manager_->conversation().GetMessages(request);
}
ApiResult<ConversationResponse> AmsClient::GetConversation(
    const ConversationRequest& request) const {
  return api_manager_->conversation().GetConversation(request);
}
ApiResult<SimpleResponse> AmsClient::DeleteConversation(
    const DeleteConversationRequest& request) const {
  return api_manager_->conversation().DeleteConversation(request);
}
ApiResult<RenameConversationResponse> AmsClient::RenameConversation(
    const RenameConversationRequest& request) const {
  return api_manager_->conversation().RenameConversation(request);
}
ApiResult<AudioToTextResponse> AmsClient::AudioToText(
    const AudioToTextRequest& request) const {
  return api_manager_->audio().AudioToText(request);
}
ApiResult<AppMetaResponse> AmsClient::AppMeta(
    const AppMetaRequest& request) const {
  return api_manager_->app().Meta(request);
}
ApiResult<AppInfoResponse> AmsClient::AppInfo(
    const AppInfoRequest& request) const {
  return api_manager_->app().Info(request);
}
ApiResult<AppParamResponse> AmsClient::AppParameters(
    const AppParamRequest& request) const {
  return api_manager_->app().Parameters(request);
}
ApiResult<WorkflowRunResponse> AmsClient::WorkflowRun(
    const WorkflowRunRequest& request,
    const StreamEventCallback& stream_event_callback) const {
  return api_manager_->workflow().WorkflowRun(request, stream_event_callback);
}
ApiResult<WorkflowRunInfoResponse> AmsClient::WorkflowRunInfo(
    const WorkflowRunInfoRequest& request) const {
  return api_manager_->workflow().WorkflowRunInfo(request);
}

ApiResult<SimpleResponse> AmsClient::WorkflowTaskStop(
    const WorkflowTaskStopRequest& request) const {
  return api_manager_->workflow().WorkflowTaskStop(request);
}
ApiResult<WorkflowLogsResponse> AmsClient::WorkflowLogs(
    const WorkflowLogsRequest& request) const {
  return api_manager_->workflow().WorkflowLogs(request);
}

// Knowledge base API implementation - Dataset Management
ApiResult<CreateDatasetResponse> AmsClient::CreateDataset(
    const CreateDatasetRequest& request) const {
  return api_manager_->knowledge().CreateDataset(request);
}

ApiResult<ListDatasetsResponse> AmsClient::ListDatasets(
    const ListDatasetsRequest& request) const {
  return api_manager_->knowledge().ListDatasets(request);
}

ApiResult<SimpleResponse> AmsClient::DeleteDataset(
    const DeleteDatasetRequest& request) const {
  return api_manager_->knowledge().DeleteDataset(request);
}

// Knowledge base API implementation - Document Management
ApiResult<CreateDocumentByTextResponse> AmsClient::CreateDocumentByText(
    const CreateDocumentByTextRequest& request) const {
  return api_manager_->knowledge().CreateDocumentByText(request);
}

ApiResult<CreateDocumentByTextResponse> AmsClient::CreateDocumentByFile(
    const CreateDocumentByFileRequest& request) const {
  return api_manager_->knowledge().CreateDocumentByFile(request);
}

ApiResult<CreateDocumentByTextResponse> AmsClient::UpdateDocumentByText(
    const UpdateDocumentByTextRequest& request) const {
  return api_manager_->knowledge().UpdateDocumentByText(request);
}

ApiResult<CreateDocumentByTextResponse> AmsClient::UpdateDocumentByFile(
    const UpdateDocumentByFileRequest& request) const {
  return api_manager_->knowledge().UpdateDocumentByFile(request);
}

ApiResult<GetIndexingStatusResponse> AmsClient::GetIndexingStatus(
    const GetIndexingStatusRequest& request) const {
  return api_manager_->knowledge().GetIndexingStatus(request);
}

ApiResult<DeleteDocumentResponse> AmsClient::DeleteDocument(
    const DeleteDocumentRequest& request) const {
  return api_manager_->knowledge().DeleteDocument(request);
}

ApiResult<ListDocumentsResponse> AmsClient::ListDocuments(
    const ListDocumentsRequest& request) const {
  return api_manager_->knowledge().ListDocuments(request);
}

// Knowledge base API implementation - Segment Management
ApiResult<CreateSegmentResponse> AmsClient::CreateSegment(
    const CreateSegmentRequest& request) const {
  return api_manager_->knowledge().CreateSegment(request);
}

ApiResult<ListSegmentsResponse> AmsClient::ListSegments(
    const ListSegmentsRequest& request) const {
  return api_manager_->knowledge().ListSegments(request);
}

ApiResult<DeleteSegmentResponse> AmsClient::DeleteSegment(
    const DeleteSegmentRequest& request) const {
  return api_manager_->knowledge().DeleteSegment(request);
}

ApiResult<UpdateSegmentResponse> AmsClient::UpdateSegment(
    const UpdateSegmentRequest& request) const {
  return api_manager_->knowledge().UpdateSegment(request);
}

// Knowledge base API implementation - Retrieval
ApiResult<RetrieveDatasetResponse> AmsClient::RetrieveDataset(
    const RetrieveDatasetRequest& request) const {
  return api_manager_->knowledge().RetrieveDataset(request);
}

}  // namespace amssdk
