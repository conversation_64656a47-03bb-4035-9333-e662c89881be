#ifndef AMSSDK_KNOWLEDGE_H
#define AMSSDK_KNOWLEDGE_H

#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include "include/common/common.h"

namespace amssdk {
struct PreProcessingRule {
  std::string id;
  bool enabled = false;
};
struct SegmentationRule {
  std::string separator = "\\n";
  int max_tokens = 1000;
};
struct ProcessRule {
  std::string mode = "automatic";
  nlohmann::json rules;
  std::vector<PreProcessingRule> pre_processing_rules;
  SegmentationRule segmentation;
};

struct DataSourceInfo {
  std::string upload_file_id;
};

struct DocumentInfo {
  std::string id;
  int position = 0;
  std::string data_source_type;
  DataSourceInfo data_source_info;
  std::string dataset_process_rule_id;
  std::string name;
  std::string created_from;
  std::string created_by;
  long created_at = 0;
  int tokens = 0;
  std::string indexing_status;
  std::string error;
  bool enabled = true;
  long disabled_at = 0;
  std::string disabled_by;
  bool archived = false;
  std::string display_status;
  int word_count = 0;
  int hit_count = 0;
  std::string doc_form;
};

class CreateDocumentByTextRequest {
 public:
  CreateDocumentByTextRequest() = default;
  CreateDocumentByTextRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  CreateDocumentByTextRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  CreateDocumentByTextRequest& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  CreateDocumentByTextRequest& SetIndexingTechnique(
      const std::string& technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDocumentByTextRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }
  // Getters
  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetText() const noexcept { return text_; }
  const std::string& GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

 private:
  std::string dataset_id_;
  std::string name_;
  std::string text_;
  std::string indexing_technique_ = "high_quality";  // high_quality 或 economy
  ProcessRule process_rule_;
};

class CreateDocumentByTextResponse {
 public:
  CreateDocumentByTextResponse() = default;

  // Setters
  void SetDocument(const DocumentInfo& document) { document_ = document; }
  void SetBatch(const std::string& batch) { batch_ = batch; }

  // Getters
  const DocumentInfo& GetDocument() const noexcept { return document_; }
  const std::string& GetBatch() const noexcept { return batch_; }

 private:
  DocumentInfo document_;
  std::string batch_;
};

}  // namespace amssdk

#endif  // AMSSDK_KNOWLEDGE_H
