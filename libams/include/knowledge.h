#ifndef AMSSDK_KNOWLEDGE_H
#define AMSSDK_KNOWLEDGE_H

#include <experimental/filesystem>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include "include/common/common.h"

namespace amssdk {
struct PreProcessingRule {
  std::string id;
  bool enabled = false;
};
struct SegmentationRule {
  std::string separator = R"(\n)";
  int max_tokens = 1000;
};
struct ProcessRule {
  enum class Mode { AUTOMATIC, CUSTOM };
  Mode mode = Mode::AUTOMATIC;
  nlohmann::json rules;
  std::vector<PreProcessingRule> pre_processing_rules;
  SegmentationRule segmentation;
};

struct DataSourceInfo {
  std::string upload_file_id;
};

// 知识库信息
struct DatasetInfo {
  std::string id;
  std::string name;
  std::string description;
  std::string provider;
  std::string permission;
  std::string data_source_type;
  std::string indexing_technique;
  int app_count = 0;
  int document_count = 0;
  int word_count = 0;
  std::string created_by;
  long created_at = 0;
  std::string updated_by;
  long updated_at = 0;
  std::string embedding_model;
  std::string embedding_model_provider;
  bool embedding_available = false;
};

// 文档分段信息
struct SegmentInfo {
  std::string id;
  int position = 0;
  std::string document_id;
  std::string content;
  std::string answer;
  int word_count = 0;
  int tokens = 0;
  std::vector<std::string> keywords;
  std::string index_node_id;
  std::string index_node_hash;
  int hit_count = 0;
  bool enabled = true;
  long disabled_at = 0;
  std::string disabled_by;
  std::string status;
  std::string created_by;
  long created_at = 0;
  long indexing_at = 0;
  long completed_at = 0;
  std::string error;
  long stopped_at = 0;
};

// 索引状态信息
struct IndexingStatusInfo {
  std::string id;
  std::string indexing_status;
  double processing_started_at = 0.0;
  double parsing_completed_at = 0.0;
  double cleaning_completed_at = 0.0;
  double splitting_completed_at = 0.0;
  double completed_at = 0.0;
  double paused_at = 0.0;
  std::string error;
  double stopped_at = 0.0;
  int completed_segments = 0;
  int total_segments = 0;
};

struct DocumentInfo {
  std::string id;
  int position = 0;
  std::string data_source_type;
  DataSourceInfo data_source_info;
  std::string dataset_process_rule_id;
  std::string name;
  std::string created_from;
  std::string created_by;
  long created_at = 0;
  int tokens = 0;
  std::string indexing_status;
  std::string error;
  bool enabled = true;
  long disabled_at = 0;
  std::string disabled_by;
  bool archived = false;
  std::string display_status;
  int word_count = 0;
  int hit_count = 0;
  std::string doc_form;
};

class CreateDocumentByTextRequest {
 public:
  CreateDocumentByTextRequest() = default;
  CreateDocumentByTextRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  CreateDocumentByTextRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  CreateDocumentByTextRequest& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  CreateDocumentByTextRequest& SetIndexingTechnique(
      const std::string& technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDocumentByTextRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }
  // Getters
  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetText() const noexcept { return text_; }
  const std::string& GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

 private:
  std::string dataset_id_;
  std::string name_;
  std::string text_;
  std::string indexing_technique_ = "high_quality";  // high_quality 或 economy
  ProcessRule process_rule_;
};

class CreateDocumentByTextResponse {
 public:
  CreateDocumentByTextResponse() = default;

  // Setters
  void SetDocument(const DocumentInfo& document) { document_ = document; }
  void SetBatch(const std::string& batch) { batch_ = batch; }

  // Getters
  const DocumentInfo& GetDocument() const noexcept { return document_; }
  const std::string& GetBatch() const noexcept { return batch_; }

 private:
  DocumentInfo document_;
  std::string batch_;
};

// ===== 创建知识库 =====
class CreateDatasetRequest {
 public:
  CreateDatasetRequest() = default;

  CreateDatasetRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  CreateDatasetRequest& SetDescription(const std::string& description) {
    description_ = description;
    return *this;
  }
  CreateDatasetRequest& SetIndexingTechnique(const std::string& technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDatasetRequest& SetPermission(const std::string& permission) {
    permission_ = permission;
    return *this;
  }
  CreateDatasetRequest& SetProvider(const std::string& provider) {
    provider_ = provider;
    return *this;
  }

  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetDescription() const noexcept { return description_; }
  const std::string& GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }
  const std::string& GetPermission() const noexcept { return permission_; }
  const std::string& GetProvider() const noexcept { return provider_; }

 private:
  std::string name_;
  std::string description_;
  std::string indexing_technique_;
  std::string permission_ = "only_me";
  std::string provider_ = "vendor";
};

class CreateDatasetResponse {
 public:
  void SetDataset(const DatasetInfo& dataset) { dataset_ = dataset; }
  const DatasetInfo& GetDataset() const noexcept { return dataset_; }

 private:
  DatasetInfo dataset_;
};

// ===== 知识库列表 =====
class ListDatasetsRequest {
 public:
  ListDatasetsRequest& SetPage(int page) {
    page_ = page;
    return *this;
  }
  ListDatasetsRequest& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }

  int GetPage() const noexcept { return page_; }
  int GetLimit() const noexcept { return limit_; }

 private:
  int page_ = 1;
  int limit_ = 20;
};

class ListDatasetsResponse {
 public:
  void SetData(const std::vector<DatasetInfo>& data) { data_ = data; }
  void SetHasMore(bool has_more) { has_more_ = has_more; }
  void SetLimit(int limit) { limit_ = limit; }
  void SetTotal(int total) { total_ = total; }
  void SetPage(int page) { page_ = page; }

  const std::vector<DatasetInfo>& GetData() const noexcept { return data_; }
  bool GetHasMore() const noexcept { return has_more_; }
  int GetLimit() const noexcept { return limit_; }
  int GetTotal() const noexcept { return total_; }
  int GetPage() const noexcept { return page_; }

 private:
  std::vector<DatasetInfo> data_;
  bool has_more_ = false;
  int limit_ = 20;
  int total_ = 0;
  int page_ = 1;
};

// ===== 删除知识库 =====
class DeleteDatasetRequest {
 public:
  DeleteDatasetRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }

 private:
  std::string dataset_id_;
};

}  // namespace amssdk

#endif  // AMSSDK_KNOWLEDGE_H
