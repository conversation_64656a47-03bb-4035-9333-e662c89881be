#ifndef AMSSDK_KNOWLEDGE_EXTENDED_H
#define AMSSDK_KNOWLEDGE_EXTENDED_H

#include "knowledge.h"

namespace amssdk {

// ===== 通过文件创建文档 =====
class CreateDocumentByFileRequest {
 public:
  CreateDocumentByFileRequest() = default;

  CreateDocumentByFileRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  CreateDocumentByFileRequest& SetFile(const std::experimental::filesystem::path& file) {
    file_ = file;
    return *this;
  }
  CreateDocumentByFileRequest& SetIndexingTechnique(const std::string& technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDocumentByFileRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }
  CreateDocumentByFileRequest& SetOriginalDocumentId(const std::string& id) {
    original_document_id_ = id;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::experimental::filesystem::path& GetFile() const noexcept { return file_; }
  const std::string& GetIndexingTechnique() const noexcept { return indexing_technique_; }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }
  const std::string& GetOriginalDocumentId() const noexcept { return original_document_id_; }

 private:
  std::string dataset_id_;
  std::experimental::filesystem::path file_;
  std::string indexing_technique_ = "high_quality";
  ProcessRule process_rule_;
  std::string original_document_id_;
};

// ===== 更新文档（通过文本） =====
class UpdateDocumentByTextRequest {
 public:
  UpdateDocumentByTextRequest() = default;

  UpdateDocumentByTextRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  UpdateDocumentByTextRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  UpdateDocumentByTextRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  UpdateDocumentByTextRequest& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  UpdateDocumentByTextRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetText() const noexcept { return text_; }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string name_;
  std::string text_;
  ProcessRule process_rule_;
};

// ===== 更新文档（通过文件） =====
class UpdateDocumentByFileRequest {
 public:
  UpdateDocumentByFileRequest() = default;

  UpdateDocumentByFileRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  UpdateDocumentByFileRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  UpdateDocumentByFileRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  UpdateDocumentByFileRequest& SetFile(const std::experimental::filesystem::path& file) {
    file_ = file;
    return *this;
  }
  UpdateDocumentByFileRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::experimental::filesystem::path& GetFile() const noexcept { return file_; }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string name_;
  std::experimental::filesystem::path file_;
  ProcessRule process_rule_;
};

// ===== 获取文档索引状态 =====
class GetIndexingStatusRequest {
 public:
  GetIndexingStatusRequest() = default;

  GetIndexingStatusRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  GetIndexingStatusRequest& SetBatch(const std::string& batch) {
    batch_ = batch;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetBatch() const noexcept { return batch_; }

 private:
  std::string dataset_id_;
  std::string batch_;
};

class GetIndexingStatusResponse {
 public:
  void SetData(const std::vector<IndexingStatusInfo>& data) { data_ = data; }
  const std::vector<IndexingStatusInfo>& GetData() const noexcept { return data_; }

 private:
  std::vector<IndexingStatusInfo> data_;
};

// ===== 删除文档 =====
class DeleteDocumentRequest {
 public:
  DeleteDocumentRequest() = default;

  DeleteDocumentRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  DeleteDocumentRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
};

class DeleteDocumentResponse {
 public:
  void SetResult(const std::string& result) { result_ = result; }
  const std::string& GetResult() const noexcept { return result_; }

 private:
  std::string result_;
};

// ===== 文档列表 =====
class ListDocumentsRequest {
 public:
  ListDocumentsRequest() = default;

  ListDocumentsRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  ListDocumentsRequest& SetKeyword(const std::string& keyword) {
    keyword_ = keyword;
    return *this;
  }
  ListDocumentsRequest& SetPage(int page) {
    page_ = page;
    return *this;
  }
  ListDocumentsRequest& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetKeyword() const noexcept { return keyword_; }
  int GetPage() const noexcept { return page_; }
  int GetLimit() const noexcept { return limit_; }

 private:
  std::string dataset_id_;
  std::string keyword_;
  int page_ = 1;
  int limit_ = 20;
};

class ListDocumentsResponse {
 public:
  void SetData(const std::vector<DocumentInfo>& data) { data_ = data; }
  void SetHasMore(bool has_more) { has_more_ = has_more; }
  void SetLimit(int limit) { limit_ = limit; }
  void SetTotal(int total) { total_ = total; }
  void SetPage(int page) { page_ = page; }

  const std::vector<DocumentInfo>& GetData() const noexcept { return data_; }
  bool GetHasMore() const noexcept { return has_more_; }
  int GetLimit() const noexcept { return limit_; }
  int GetTotal() const noexcept { return total_; }
  int GetPage() const noexcept { return page_; }

 private:
  std::vector<DocumentInfo> data_;
  bool has_more_ = false;
  int limit_ = 20;
  int total_ = 0;
  int page_ = 1;
};

}  // namespace amssdk

#endif  // AMSSDK_KNOWLEDGE_EXTENDED_H
