#ifndef AMSSDK_KNOWLEDGE_SERVICE_H
#define AMSSDK_KNOWLEDGE_SERVICE_H

#include "include/common/api_result.h"

namespace amssdk {

class ApiManager;
class HttpClient;
class Authorization;
class CreateDocumentByTextRequest;
class CreateDocumentByTextResponse;

class KnowledgeService {
 public:
  LIBAMS_EXPORT
  explicit KnowledgeService(ApiManager& api_manager);

  LIBAMS_EXPORT
  ApiResult<CreateDocumentByTextResponse> CreateDocumentByText(
      const CreateDocumentByTextRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};

}  // namespace amssdk

#endif  // AMSSDK_KNOWLEDGE_SERVICE_H
