#ifndef AMSSDK_KNOWLEDGE_SERVICE_H
#define AMSSDK_KNOWLEDGE_SERVICE_H

#include "include/common/api_result.h"

namespace amssdk {

class ApiManager;
class HttpClient;
class Authorization;

// 前向声明所有请求和响应类
class CreateDocumentByTextRequest;
class CreateDocumentByTextResponse;
class CreateDocumentByFileRequest;
class UpdateDocumentByTextRequest;
class UpdateDocumentByFileRequest;
class GetIndexingStatusRequest;
class GetIndexingStatusResponse;
class DeleteDocumentRequest;
class DeleteDocumentResponse;
class ListDocumentsRequest;
class ListDocumentsResponse;
class CreateDatasetRequest;
class CreateDatasetResponse;
class ListDatasetsRequest;
class ListDatasetsResponse;
class DeleteDatasetRequest;
class CreateSegmentRequest;
class CreateSegmentResponse;
class ListSegmentsRequest;
class ListSegmentsResponse;
class DeleteSegmentRequest;
class DeleteSegmentResponse;
class UpdateSegmentRequest;
class UpdateSegmentResponse;
class RetrieveDatasetRequest;
class RetrieveDatasetResponse;
class SimpleResponse;

class KnowledgeService {
 public:
  LIBAMS_EXPORT
  explicit KnowledgeService(ApiManager& api_manager);

  // 知识库管理
  LIBAMS_EXPORT
  ApiResult<CreateDatasetResponse> CreateDataset(
      const CreateDatasetRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListDatasetsResponse> ListDatasets(
      const ListDatasetsRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> DeleteDataset(
      const DeleteDatasetRequest& request) const;

  // 文档管理
  LIBAMS_EXPORT
  ApiResult<CreateDocumentByTextResponse> CreateDocumentByText(
      const CreateDocumentByTextRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentByTextResponse> CreateDocumentByFile(
      const CreateDocumentByFileRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentByTextResponse> UpdateDocumentByText(
      const UpdateDocumentByTextRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentByTextResponse> UpdateDocumentByFile(
      const UpdateDocumentByFileRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<GetIndexingStatusResponse> GetIndexingStatus(
      const GetIndexingStatusRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<DeleteDocumentResponse> DeleteDocument(
      const DeleteDocumentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListDocumentsResponse> ListDocuments(
      const ListDocumentsRequest& request) const;

  // 分段管理
  LIBAMS_EXPORT
  ApiResult<CreateSegmentResponse> CreateSegment(
      const CreateSegmentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListSegmentsResponse> ListSegments(
      const ListSegmentsRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<DeleteSegmentResponse> DeleteSegment(
      const DeleteSegmentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<UpdateSegmentResponse> UpdateSegment(
      const UpdateSegmentRequest& request) const;

  // 检索
  LIBAMS_EXPORT
  ApiResult<RetrieveDatasetResponse> RetrieveDataset(
      const RetrieveDatasetRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};

}  // namespace amssdk

#endif  // AMSSDK_KNOWLEDGE_SERVICE_H
