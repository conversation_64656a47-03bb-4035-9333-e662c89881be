#include "knowledge_service.h"

#include <nlohmann/json.hpp>
#include "api/api_manager.h"
#include "include/common/endpoints.h"
#include "include/knowledge.h"
#include "serializer/deserializer.h"
#include "serializer/serializer.h"

namespace amssdk {

KnowledgeService::KnowledgeService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<CreateDocumentByTextResponse> KnowledgeService::CreateDocumentByText(
    const CreateDocumentByTextRequest& request) const {
  
  // 序列化请求
  const nlohmann::json json_data = SerializeCreateDocumentByTextRequest(request);
  
  // 构建端点URL
  std::string endpoint = endpoints::CreateDocumentByText(request.GetDatasetId());
  
  // 发送POST请求
  auto response = http_client_.Post(json_data, endpoint);
  
  // 构建结果
  auto result = BuildResult<CreateDocumentByTextResponse>(response);
  
  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDocumentByTextResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize CreateDocumentByTextResponse: " + std::string(e.what());
    }
  }
  
  return result;
}

}  // namespace amssdk
