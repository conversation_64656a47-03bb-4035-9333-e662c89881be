#include "knowledge_service.h"

#include <nlohmann/json.hpp>
#include "api/api_manager.h"
#include "include/common/endpoints.h"
#include "include/knowledge.h"
#include "include/knowledge_extended.h"
#include "include/knowledge_segments.h"
#include "serializer/deserializer.h"
#include "serializer/serializer.h"

namespace amssdk {

KnowledgeService::KnowledgeService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

// 知识库管理实现
ApiResult<CreateDatasetResponse> KnowledgeService::CreateDataset(
    const CreateDatasetRequest& request) const {
  const nlohmann::json json_data = SerializeCreateDatasetRequest(request);
  auto response = http_client_.Post(json_data, endpoints::kDatasets);
  auto result = BuildResult<CreateDatasetResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDatasetResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize CreateDatasetResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

ApiResult<ListDatasetsResponse> KnowledgeService::ListDatasets(
    const ListDatasetsRequest& request) const {
  std::string query_params = SerializeListDatasetsRequest(request);
  std::string endpoint = std::string(endpoints::kDatasets) + "?" + query_params;
  auto response = http_client_.Get(endpoint);
  auto result = BuildResult<ListDatasetsResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeListDatasetsResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize ListDatasetsResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

ApiResult<SimpleResponse> KnowledgeService::DeleteDataset(
    const DeleteDatasetRequest& request) const {
  std::string endpoint = endpoints::DeleteDataset(request.GetDatasetId());
  //fixme: Delete
  auto response = http_client_.Delete("", endpoint);
  auto result = BuildResult<SimpleResponse>(response);
  result.data.result = result.success ? SimpleResponse::ResultType::kSuccess
                                      : SimpleResponse::ResultType::kFailure;
  return result;
}

// 文档管理实现
ApiResult<CreateDocumentByTextResponse> KnowledgeService::CreateDocumentByText(
    const CreateDocumentByTextRequest& request) const {
  const nlohmann::json json_data =
      SerializeCreateDocumentByTextRequest(request);
  std::string endpoint =
      endpoints::CreateDocumentByText(request.GetDatasetId());
  auto response = http_client_.Post(json_data, endpoint);
  auto result = BuildResult<CreateDocumentByTextResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDocumentByTextResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize CreateDocumentByTextResponse: " +
          std::string(e.what());
    }
  }
  return result;
}

ApiResult<CreateDocumentByTextResponse> KnowledgeService::CreateDocumentByFile(
    const CreateDocumentByFileRequest& request) const {
  // 构建multipart form data
  std::map<std::string, std::string> form_data;
  form_data["file"] = request.GetFile().string();

  // 序列化其他数据
  nlohmann::json data_json = SerializeCreateDocumentByFileRequest(request);
  form_data["data"] = data_json.dump();

  std::string endpoint =
      endpoints::CreateDocumentByFile(request.GetDatasetId());
  auto response = http_client_.MultipartForm(endpoint, form_data);
  auto result = BuildResult<CreateDocumentByTextResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDocumentByTextResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize CreateDocumentByTextResponse: " +
          std::string(e.what());
    }
  }
  return result;
}

ApiResult<CreateDocumentByTextResponse> KnowledgeService::UpdateDocumentByText(
    const UpdateDocumentByTextRequest& request) const {
  const nlohmann::json json_data =
      SerializeUpdateDocumentByTextRequest(request);
  std::string endpoint = endpoints::UpdateDocumentByText(
      request.GetDatasetId(), request.GetDocumentId());
  auto response = http_client_.Post(json_data, endpoint);
  auto result = BuildResult<CreateDocumentByTextResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDocumentByTextResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize CreateDocumentByTextResponse: " +
          std::string(e.what());
    }
  }
  return result;
}

ApiResult<CreateDocumentByTextResponse> KnowledgeService::UpdateDocumentByFile(
    const UpdateDocumentByFileRequest& request) const {
  std::map<std::string, std::string> form_data;
  form_data["file"] = request.GetFile().string();

  nlohmann::json data_json = SerializeUpdateDocumentByFileRequest(request);
  form_data["data"] = data_json.dump();

  std::string endpoint = endpoints::UpdateDocumentByFile(
      request.GetDatasetId(), request.GetDocumentId());
  auto response = http_client_.MultipartForm(endpoint, form_data);
  auto result = BuildResult<CreateDocumentByTextResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDocumentByTextResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize CreateDocumentByTextResponse: " +
          std::string(e.what());
    }
  }
  return result;
}

ApiResult<GetIndexingStatusResponse> KnowledgeService::GetIndexingStatus(
    const GetIndexingStatusRequest& request) const {
  std::string endpoint =
      endpoints::GetIndexingStatus(request.GetDatasetId(), request.GetBatch());
  auto response = http_client_.Get(endpoint);
  auto result = BuildResult<GetIndexingStatusResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeGetIndexingStatusResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize GetIndexingStatusResponse: " +
          std::string(e.what());
    }
  }
  return result;
}

ApiResult<DeleteDocumentResponse> KnowledgeService::DeleteDocument(
    const DeleteDocumentRequest& request) const {
  std::string endpoint = endpoints::DeleteDocument(request.GetDatasetId(),
                                                   request.GetDocumentId());
  auto response = http_client_.Delete({}, endpoint);
  auto result = BuildResult<DeleteDocumentResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeDeleteDocumentResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize DeleteDocumentResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

ApiResult<ListDocumentsResponse> KnowledgeService::ListDocuments(
    const ListDocumentsRequest& request) const {
  std::string query_params = SerializeListDocumentsRequest(request);
  std::string endpoint =
      endpoints::ListDocuments(request.GetDatasetId()) + "?" + query_params;
  auto response = http_client_.Get(endpoint);
  auto result = BuildResult<ListDocumentsResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeListDocumentsResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize ListDocumentsResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

// 分段管理实现
ApiResult<CreateSegmentResponse> KnowledgeService::CreateSegment(
    const CreateSegmentRequest& request) const {
  const nlohmann::json json_data = SerializeCreateSegmentRequest(request);
  std::string endpoint =
      endpoints::CreateSegment(request.GetDatasetId(), request.GetDocumentId());
  auto response = http_client_.Post(json_data, endpoint);
  auto result = BuildResult<CreateSegmentResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateSegmentResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize CreateSegmentResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

ApiResult<ListSegmentsResponse> KnowledgeService::ListSegments(
    const ListSegmentsRequest& request) const {
  std::string query_params = SerializeListSegmentsRequest(request);
  std::string endpoint =
      endpoints::ListSegments(request.GetDatasetId(), request.GetDocumentId()) +
      "?" + query_params;
  auto response = http_client_.Get(endpoint);
  auto result = BuildResult<ListSegmentsResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeListSegmentsResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize ListSegmentsResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

ApiResult<DeleteSegmentResponse> KnowledgeService::DeleteSegment(
    const DeleteSegmentRequest& request) const {
  std::string endpoint = endpoints::DeleteSegment(
      request.GetDatasetId(), request.GetDocumentId(), request.GetSegmentId());
  auto response = http_client_.Delete({}, endpoint);
  auto result = BuildResult<DeleteSegmentResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeDeleteSegmentResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize DeleteSegmentResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

ApiResult<UpdateSegmentResponse> KnowledgeService::UpdateSegment(
    const UpdateSegmentRequest& request) const {
  const nlohmann::json json_data = SerializeUpdateSegmentRequest(request);
  std::string endpoint = endpoints::UpdateSegment(
      request.GetDatasetId(), request.GetDocumentId(), request.GetSegmentId());
  auto response = http_client_.Post(json_data, endpoint);
  auto result = BuildResult<UpdateSegmentResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeUpdateSegmentResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize UpdateSegmentResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

// 检索实现
ApiResult<RetrieveDatasetResponse> KnowledgeService::RetrieveDataset(
    const RetrieveDatasetRequest& request) const {
  const nlohmann::json json_data = SerializeRetrieveDatasetRequest(request);
  std::string endpoint = endpoints::RetrieveDataset(request.GetDatasetId());
  auto response = http_client_.Post(json_data, endpoint);
  auto result = BuildResult<RetrieveDatasetResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeRetrieveDatasetResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize RetrieveDatasetResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}

}  // namespace amssdk
