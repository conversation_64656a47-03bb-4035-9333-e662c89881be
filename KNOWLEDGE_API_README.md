# 知识库 API C++ SDK

本文档介绍如何使用 C++ SDK 调用知识库相关的 API。

## 概述

知识库 API 提供了完整的知识库管理功能，包括：

- **知识库管理**：创建、列表、删除知识库
- **文档管理**：通过文本或文件创建/更新文档，获取索引状态，删除文档
- **分段管理**：创建、查询、更新、删除文档分段
- **检索功能**：在知识库中检索相关内容

## 头文件结构

```cpp
#include "include/knowledge.h"           // 基础数据结构和创建文档API
#include "include/knowledge_extended.h"  // 扩展的文档管理API
#include "include/knowledge_segments.h"  // 分段管理和检索API
```

## API 分类

### 1. 知识库管理

#### 创建知识库
```cpp
CreateDatasetRequest request;
request.SetName("测试知识库")
       .SetDescription("这是一个测试知识库")
       .SetIndexingTechnique("high_quality")  // 或 "economy"
       .SetPermission("only_me");             // 或 "all_team_members", "partial_members"

auto result = client.CreateDataset(request);
```

#### 列出知识库
```cpp
ListDatasetsRequest request;
request.SetPage(1).SetLimit(20);

auto result = client.ListDatasets(request);
```

#### 删除知识库
```cpp
DeleteDatasetRequest request;
request.SetDatasetId("dataset-id");

auto result = client.DeleteDataset(request);
```

### 2. 文档管理

#### 通过文本创建文档
```cpp
CreateDocumentByTextRequest request;
request.SetDatasetId("dataset-id")
       .SetName("文档名称")
       .SetText("文档内容")
       .SetIndexingTechnique("high_quality");

ProcessRule process_rule;
process_rule.mode = "automatic";  // 或 "custom"
request.SetProcessRule(process_rule);

auto result = client.CreateDocumentByText(request);
```

#### 通过文件创建文档
```cpp
CreateDocumentByFileRequest request;
request.SetDatasetId("dataset-id")
       .SetFile("/path/to/file")
       .SetIndexingTechnique("high_quality");

auto result = client.CreateDocumentByFile(request);
```

#### 更新文档（通过文本）
```cpp
UpdateDocumentByTextRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id")
       .SetName("新文档名称")
       .SetText("新文档内容");

auto result = client.UpdateDocumentByText(request);
```

#### 更新文档（通过文件）
```cpp
UpdateDocumentByFileRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id")
       .SetFile("/path/to/new/file");

auto result = client.UpdateDocumentByFile(request);
```

#### 获取文档索引状态
```cpp
GetIndexingStatusRequest request;
request.SetDatasetId("dataset-id")
       .SetBatch("batch-id");

auto result = client.GetIndexingStatus(request);
```

#### 删除文档
```cpp
DeleteDocumentRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id");

auto result = client.DeleteDocument(request);
```

#### 列出文档
```cpp
ListDocumentsRequest request;
request.SetDatasetId("dataset-id")
       .SetKeyword("搜索关键词")  // 可选
       .SetPage(1)
       .SetLimit(20);

auto result = client.ListDocuments(request);
```

### 3. 分段管理

#### 创建分段
```cpp
CreateSegmentRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id");

std::vector<SegmentInfo> segments;
SegmentInfo segment;
segment.content = "分段内容";
segment.answer = "答案内容";  // 可选
segment.keywords = {"关键词1", "关键词2"};  // 可选
segments.push_back(segment);

request.SetSegments(segments);
auto result = client.CreateSegment(request);
```

#### 查询分段
```cpp
ListSegmentsRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id")
       .SetKeyword("搜索关键词")  // 可选
       .SetStatus("completed");   // 可选

auto result = client.ListSegments(request);
```

#### 更新分段
```cpp
UpdateSegmentRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id")
       .SetSegmentId("segment-id");

SegmentInfo segment;
segment.content = "新的分段内容";
segment.answer = "新的答案";
segment.enabled = true;
request.SetSegment(segment);

auto result = client.UpdateSegment(request);
```

#### 删除分段
```cpp
DeleteSegmentRequest request;
request.SetDatasetId("dataset-id")
       .SetDocumentId("document-id")
       .SetSegmentId("segment-id");

auto result = client.DeleteSegment(request);
```

### 4. 检索功能

#### 检索知识库
```cpp
RetrieveDatasetRequest request;
request.SetDatasetId("dataset-id")
       .SetQuery("检索查询");

RetrievalModel retrieval_model;
retrieval_model.search_method = "keyword_search";  // 或其他检索方法
retrieval_model.top_k = 5;
retrieval_model.reranking_enable = false;
request.SetRetrievalModel(retrieval_model);

auto result = client.RetrieveDataset(request);
```

## 检索方法说明

- `keyword_search`: 关键字检索
- `semantic_search`: 语义检索
- `full_text_search`: 全文检索
- `hybrid_search`: 混合检索

## 索引技术说明

- `high_quality`: 高质量模式，使用 embedding 模型进行嵌入
- `economy`: 经济模式，使用关键字表索引

## 权限设置说明

- `only_me`: 仅自己
- `all_team_members`: 所有团队成员
- `partial_members`: 部分团队成员

## 错误处理

所有 API 调用都返回 `ApiResult<T>` 类型，包含：
- `ok`: 布尔值，表示请求是否成功
- `data`: 响应数据（成功时）
- `error`: 错误信息（失败时）
- `raw_body`: 原始响应体

```cpp
auto result = client.CreateDataset(request);
if (!result.ok) {
    std::cerr << "Error: " << result.error.message << std::endl;
    std::cerr << "Raw response: " << result.raw_body << std::endl;
} else {
    // 处理成功结果
    const auto& dataset = result.data.GetDataset();
    std::cout << "Created dataset ID: " << dataset.id << std::endl;
}
```

## 完整示例

参考 `examples/example_knowledge_complete.cpp` 文件，其中包含了所有知识库 API 的使用示例。

## 编译

确保在 CMakeLists.txt 中包含了所有必要的头文件和源文件，然后编译项目：

```bash
cmake --build cmake-build-debug --target libams
cmake --build cmake-build-debug --target example_knowledge_complete
```
