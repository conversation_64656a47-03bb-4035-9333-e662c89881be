#include <iostream>
#include "ams_client.h"
#include "include/knowledge.h"
#include "include/knowledge_extended.h"
#include "include/knowledge_segments.h"

using namespace amssdk;

void PrintError(const std::string& operation, const std::string& raw_body) {
  try {
    auto json = nlohmann::json::parse(raw_body);
    if (json.contains("message")) {
      std::cerr << operation << " failed: " << json["message"].get<std::string>() << std::endl;
    } else {
      std::cerr << operation << " failed: " << raw_body << std::endl;
    }
  } catch (const std::exception&) {
    std::cerr << operation << " failed: " << raw_body << std::endl;
  }
}

int main() {
  // 创建客户端
  AmsClient client("http://**********/v1");

  // 设置授权密钥
  if (!client.SetAuthorizationKey("app-AH8NRZNiWATarWbMl4zDtvo3")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  
  client.SetMaxTimeout(300000);

  std::cout << "=== Knowledge Base API Demo ===" << std::endl;

  // 1. 创建知识库
  std::cout << "\n1. Creating dataset..." << std::endl;
  CreateDatasetRequest create_dataset_req;
  create_dataset_req.SetName("测试知识库")
                   .SetDescription("这是一个测试知识库")
                   .SetIndexingTechnique("high_quality")
                   .SetPermission("only_me");

  auto create_dataset_result = client.CreateDataset(create_dataset_req);
  if (!create_dataset_result.ok) {
    PrintError("Create dataset", create_dataset_result.raw_body);
    return -1;
  }
  
  std::string dataset_id = create_dataset_result.data.GetDataset().id;
  std::cout << "Dataset created with ID: " << dataset_id << std::endl;

  // 2. 列出知识库
  std::cout << "\n2. Listing datasets..." << std::endl;
  ListDatasetsRequest list_datasets_req;
  list_datasets_req.SetPage(1).SetLimit(10);

  auto list_datasets_result = client.ListDatasets(list_datasets_req);
  if (!list_datasets_result.ok) {
    PrintError("List datasets", list_datasets_result.raw_body);
  } else {
    std::cout << "Found " << list_datasets_result.data.GetTotal() << " datasets" << std::endl;
  }

  // 3. 通过文本创建文档
  std::cout << "\n3. Creating document by text..." << std::endl;
  CreateDocumentByTextRequest create_doc_req;
  create_doc_req.SetDatasetId(dataset_id)
               .SetName("测试文档")
               .SetText("这是一个测试文档的内容。它包含了一些示例文本。")
               .SetIndexingTechnique("high_quality");

  ProcessRule process_rule;
  process_rule.mode = "automatic";
  create_doc_req.SetProcessRule(process_rule);

  auto create_doc_result = client.CreateDocumentByText(create_doc_req);
  if (!create_doc_result.ok) {
    PrintError("Create document by text", create_doc_result.raw_body);
    return -1;
  }
  
  std::string document_id = create_doc_result.data.GetDocument().id;
  std::string batch_id = create_doc_result.data.GetBatch();
  std::cout << "Document created with ID: " << document_id << std::endl;
  std::cout << "Batch ID: " << batch_id << std::endl;

  // 4. 检查索引状态
  std::cout << "\n4. Checking indexing status..." << std::endl;
  GetIndexingStatusRequest status_req;
  status_req.SetDatasetId(dataset_id).SetBatch(batch_id);

  auto status_result = client.GetIndexingStatus(status_req);
  if (!status_result.ok) {
    PrintError("Get indexing status", status_result.raw_body);
  } else {
    const auto& status_data = status_result.data.GetData();
    if (!status_data.empty()) {
      std::cout << "Indexing status: " << status_data[0].indexing_status << std::endl;
    }
  }

  // 5. 列出文档
  std::cout << "\n5. Listing documents..." << std::endl;
  ListDocumentsRequest list_docs_req;
  list_docs_req.SetDatasetId(dataset_id).SetPage(1).SetLimit(10);

  auto list_docs_result = client.ListDocuments(list_docs_req);
  if (!list_docs_result.ok) {
    PrintError("List documents", list_docs_result.raw_body);
  } else {
    std::cout << "Found " << list_docs_result.data.GetTotal() << " documents" << std::endl;
  }

  // 6. 创建分段
  std::cout << "\n6. Creating segment..." << std::endl;
  CreateSegmentRequest create_seg_req;
  create_seg_req.SetDatasetId(dataset_id).SetDocumentId(document_id);
  
  std::vector<SegmentInfo> segments;
  SegmentInfo segment;
  segment.content = "这是一个新的分段内容";
  segment.answer = "这是答案";
  segment.keywords = {"测试", "分段"};
  segments.push_back(segment);
  create_seg_req.SetSegments(segments);

  auto create_seg_result = client.CreateSegment(create_seg_req);
  if (!create_seg_result.ok) {
    PrintError("Create segment", create_seg_result.raw_body);
  } else {
    std::cout << "Segment created successfully" << std::endl;
  }

  // 7. 列出分段
  std::cout << "\n7. Listing segments..." << std::endl;
  ListSegmentsRequest list_segs_req;
  list_segs_req.SetDatasetId(dataset_id).SetDocumentId(document_id);

  auto list_segs_result = client.ListSegments(list_segs_req);
  if (!list_segs_result.ok) {
    PrintError("List segments", list_segs_result.raw_body);
  } else {
    std::cout << "Found " << list_segs_result.data.GetData().size() << " segments" << std::endl;
  }

  // 8. 检索知识库
  std::cout << "\n8. Retrieving from dataset..." << std::endl;
  RetrieveDatasetRequest retrieve_req;
  retrieve_req.SetDatasetId(dataset_id).SetQuery("测试");
  
  RetrievalModel retrieval_model;
  retrieval_model.search_method = "keyword_search";
  retrieval_model.top_k = 3;
  retrieve_req.SetRetrievalModel(retrieval_model);

  auto retrieve_result = client.RetrieveDataset(retrieve_req);
  if (!retrieve_result.ok) {
    PrintError("Retrieve dataset", retrieve_result.raw_body);
  } else {
    std::cout << "Found " << retrieve_result.data.GetRecords().size() << " records" << std::endl;
  }

  std::cout << "\n=== Demo completed ===" << std::endl;
  return 0;
}
