cmake_minimum_required(VERSION 4.0)
project(examples)

set(CMAKE_CXX_STANDARD 14)

find_package(curl REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)

include_directories(${CMAKE_CURRENT_SOURCE_DIR})

add_executable(example_chat example_chat.cpp)
add_executable(example_file_upload example_file_upload.cpp)
add_executable(example_stop_task example_stop_task.cpp)
add_executable(example_get_suggested example_get_suggested.cpp)
add_executable(example_get_messages example_get_messages.cpp)
add_executable(example_get_conversation example_get_conversation.cpp)
add_executable(example_delete_conversation example_delete_conversation.cpp)
add_executable(example_rename_conversation example_rename_conversation.cpp)
add_executable(example_send_feedback example_send_feedback.cpp)
add_executable(example_audio example_audio.cpp)
add_executable(example_app_meta example_app_meta.cpp)
add_executable(example_app_info example_app_info.cpp)
add_executable(example_app_param example_app_param.cpp)
add_executable(example_completion_message example_completion_message.cpp)
add_executable(example_workflow_run example_workflow_run.cpp)
add_executable(example_workflow_run_info example_workflow_run_info.cpp)
add_executable(example_workflow_stop_task example_workflow_stop_task.cpp)
add_executable(example_workflow_logs example_workflow_logs.cpp)

target_link_libraries(example_chat PRIVATE libams)
target_link_libraries(example_file_upload PRIVATE libams)
target_link_libraries(example_stop_task PRIVATE libams)
target_link_libraries(example_get_suggested PRIVATE libams)
target_link_libraries(example_get_messages PRIVATE libams)
target_link_libraries(example_get_conversation PRIVATE libams)
target_link_libraries(example_delete_conversation PRIVATE libams)
target_link_libraries(example_rename_conversation PRIVATE libams)
target_link_libraries(example_send_feedback PRIVATE libams)
target_link_libraries(example_audio PRIVATE libams)
target_link_libraries(example_app_meta PRIVATE libams)
target_link_libraries(example_app_info PRIVATE libams)
target_link_libraries(example_app_param PRIVATE libams)
target_link_libraries(example_completion_message PRIVATE libams)
target_link_libraries(example_workflow_run PRIVATE libams)
target_link_libraries(example_workflow_run_info PRIVATE libams)
target_link_libraries(example_workflow_stop_task PRIVATE libams)
target_link_libraries(example_workflow_logs PRIVATE libams)